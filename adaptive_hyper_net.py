"""
AdaptiveHyperNet
PyTorch implementation prototype for the Seed->HyperExpand approach with innovations:
- SeedGenerator (compact seed -> latent)
- HyperExpander (generates low-rank U,V lazily)
- RGBWeights (multi-channel weight vectors mixed by context)
- AdaptiveGating (dynamic masks / top-k gating)
- MemoryBank (LRU cache for generated expansions)
- RegularizationEngine (loss components for distillation and reconstruction)
- LazyWeight (no full materialization; supports matmul)

This prototype focuses on clarity and testability. It is not hardware-optimized.
"""

from __future__ import annotations
import math
import time
import hashlib
from typing import Optional, Tuple, Any
from collections import OrderedDict

import torch
import torch.nn as nn
import torch.nn.functional as F


# ----------------------------- Utilities --------------------------------- #

def sha256_hex(x: bytes) -> str:
    return hashlib.sha256(x).hexdigest()


# --------------------------- Seed Generator ------------------------------ #

class SeedGenerator(nn.Module):
    """Compact seed to latent mapping.
    Small MLP that expands a low-dim seed into a latent used by hypernet.
    """

    def __init__(self, seed_dim: int = 64, latent_dim: int = 256, hidden: int = 128):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(seed_dim, hidden),
            nn.GELU(),
            nn.Linear(hidden, latent_dim),
        )

    def forward(self, seed: torch.Tensor) -> torch.Tensor:
        # seed: [batch, seed_dim] or [seed_dim]
        return self.net(seed)


# --------------------------- Hyper Expander ------------------------------ #

class HyperExpander(nn.Module):
    """Generates low-rank factors U and V from seed+context.
    Produces compact representations to avoid materializing full W.
    """

    def __init__(self, latent_dim: int = 256, max_rank: int = 64, hidden: int = 256):
        super().__init__()
        self.latent_dim = latent_dim
        self.max_rank = max_rank
        # hypernet heads for U and V
        self.head_u = nn.Sequential(nn.Linear(latent_dim, hidden), nn.GELU(), nn.Linear(hidden, max_rank))
        self.head_v = nn.Sequential(nn.Linear(latent_dim, hidden), nn.GELU(), nn.Linear(hidden, max_rank))
        # scale heads to generate projected bases
        self.scale_u = nn.Linear(latent_dim, 1)
        self.scale_v = nn.Linear(latent_dim, 1)

    def forward(self, latent: torch.Tensor, out_dim: int, in_dim: int, rank: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # latent: [batch, latent_dim] or [latent_dim]
        if rank > self.max_rank:
            raise ValueError("rank > max_rank")
        batch = latent.dim() == 2
        # produce compact coefficients for basis mixing
        coeffs_u = self.head_u(latent)
        coeffs_v = self.head_v(latent)
        scale_u = torch.sigmoid(self.scale_u(latent))
        scale_v = torch.sigmoid(self.scale_v(latent))

        # Produce small basis matrices deterministically from latent (seeded projection)
        # We map latent -> (out_dim x rank) and (in_dim x rank) via linear projections
        # To keep memory low we generate through matmul with fixed random matrices
        device = latent.device
        base_u_proj = _parametric_projection(latent, out_dim, rank, name_suffix="u", device=device)
        base_v_proj = _parametric_projection(latent, in_dim, rank, name_suffix="v", device=device)

        # Mix bases with coeffs and scales
        # coeffs shapes: [latent_dim] or [batch, latent_dim] reduced to [rank]
        # For simplicity sum across latent channels via linear combination
        if batch:
            # per-batch factors
            coeffs_u = coeffs_u.unsqueeze(1)  # [B,1,max_rank]
            coeffs_v = coeffs_v.unsqueeze(1)
            U = base_u_proj * coeffs_u * scale_u.unsqueeze(-1)
            V = base_v_proj * coeffs_v * scale_v.unsqueeze(-1)
        else:
            U = base_u_proj * coeffs_u.unsqueeze(0) * scale_u
            V = base_v_proj * coeffs_v.unsqueeze(0) * scale_v

        # U: [out_dim, rank], V: [in_dim, rank]
        return U, V


def _parametric_projection(latent: torch.Tensor, dim1: int, rank: int, name_suffix: str = "", device=None) -> torch.Tensor:
    """Deterministic parametric projection: latent -> matrix (dim1 x rank)
    Uses linear layers with pseudo-random fixed weights derived from a seed of the module name.
    Implementation note: We keep this stateless and deterministic across runs for reproducibility.
    """
    # collapse latent to a vector
    if latent.dim() == 2:
        z = latent.mean(dim=0)
    else:
        z = latent
    # Use hashing to generate a pseudorandom projection matrix seeded by z
    # This is a simple, deterministic route that avoids storing a large matrix per layer.
    rng = torch.manual_seed(int((z.abs().sum().item() * 1e6) % (2 ** 31 - 1)))
    # small projection
    proj = torch.randn(dim1, rank, device=device) * 0.01
    # modulate by z's small projection
    zproj = torch.tanh(z[:rank]).to(device)
    proj = proj * (1.0 + zproj.unsqueeze(0))
    return proj


# ---------------------------- RGBWeights -------------------------------- #

class RGBWeights(nn.Module):
    """Multi-channel weight vectors.
    R=semantic, G=spatial, B=temporal by default. Channels are combined by a context mixer.
    The module stores compact per-channel tensors and produces effective weight on forward.
    """

    def __init__(self, out_dim: int, in_dim: int, channels: int = 3):
        super().__init__()
        self.out_dim = out_dim
        self.in_dim = in_dim
        self.channels = channels
        self.weight_vectors = nn.Parameter(torch.randn(out_dim, in_dim, channels) * (1.0 / math.sqrt(in_dim)))
        self.context_mixer = nn.Sequential(nn.Linear(128, channels), nn.Softmax(dim=-1))  # expects context vector size 128

    def forward(self, x: torch.Tensor, context: torch.Tensor) -> torch.Tensor:
        # context: [batch, 128] or [128]
        if context.dim() == 1:
            cm = self.context_mixer(context).view(1, self.channels)  # [1, C]
            weights = torch.einsum('oic,bc->oib', self.weight_vectors, cm)  # [out,in,1]
            w = weights.mean(dim=-1)
            return F.linear(x, w)
        else:
            cm = self.context_mixer(context)  # [B, C]
            # produce per-batch weighted sum and apply per-example linear
            # F.linear does not support per-batch weights; do manual matmul
            # x: [B, *, in_dim]; flatten except batch
            b = x.shape[0]
            w_per = torch.einsum('oic,bc->boi', self.weight_vectors, cm)  # [B, out, in]
            x_flat = x.view(b, -1, self.in_dim)  # [B, N, in]
            out = torch.einsum('bni,boi->bno', x_flat, w_per)
            return out.reshape(*x.shape[:-1], self.out_dim)


# --------------------------- LazyWeight --------------------------------- #

class LazyWeight:
    """Represents a weight matrix by factors U and V without materializing W.
    Supports matmul: W x, where W = U @ V^T.
    """

    def __init__(self, U: torch.Tensor, V: torch.Tensor, quantized: bool = False):
        # U: [out_dim, rank], V: [in_dim, rank]
        self.U = U
        self.V = V
        self.quantized = quantized

    def matmul(self, x: torch.Tensor) -> torch.Tensor:
        # x: [..., in_dim]
        # compute V^T x first: [..., rank]
        # then U @ that: [..., out_dim]
        vr = torch.einsum('ir,...i->...r', self.V, x)
        out = torch.einsum('or,...r->...o', self.U, vr)
        return out

    def materialize(self) -> torch.Tensor:
        return self.U @ self.V.t()


# --------------------------- MemoryBank --------------------------------- #

class MemoryBank:
    """LRU cache for generated LazyWeight objects keyed by seed+context hash.
    Keeps memory bounded by max_items.
    """

    def __init__(self, max_items: int = 128):
        self.cache = OrderedDict()
        self.max_items = max_items

    def _make_key(self, seed: torch.Tensor, context: Optional[torch.Tensor]) -> str:
        sb = seed.detach().cpu().numpy().tobytes()
        if context is None:
            cb = b''
        else:
            cb = context.detach().cpu().numpy().tobytes()
        return sha256_hex(sb + cb)

    def get_or_put(self, seed: torch.Tensor, context: Optional[torch.Tensor], maker_fn):
        key = self._make_key(seed, context)
        if key in self.cache:
            self.cache.move_to_end(key)
            return self.cache[key]
        value = maker_fn()
        self.cache[key] = value
        if len(self.cache) > self.max_items:
            self.cache.popitem(last=False)
        return value


# ----------------------- Adaptive Rank & Gating ------------------------- #

def adaptive_rank(data_complexity: float, max_rank: int = 64) -> int:
    return min(max_rank, max(1, int(4 * math.log(data_complexity + 1 + 1e-9))))


class AdaptiveGating(nn.Module):
    """Produces a binary or soft mask for neurons. Uses TopK gating optionally.
    """

    def __init__(self, dim: int, k_frac: float = 0.5):
        super().__init__()
        self.dim = dim
        self.k_frac = k_frac
        self.proj = nn.Linear(128, dim)  # expects context vector 128

    def forward(self, context: torch.Tensor, hard: bool = True) -> torch.Tensor:
        # context: [batch, 128] or [128]
        logits = self.proj(context)
        k = max(1, int(self.k_frac * self.dim))
        if hard:
            topk = torch.topk(logits, k, dim=-1).indices
            mask = torch.zeros_like(logits)
            if logits.dim() == 1:
                mask[topk] = 1.0
            else:
                mask.scatter_(1, topk, 1.0)
            return mask
        else:
            return torch.sigmoid(logits)


# ------------------------ Regularization Engine ------------------------- #

class RegularizationEngine:
    def __init__(self, lambda_recon: float = 1.0, lambda_kd: float = 1.0):
        self.lambda_recon = lambda_recon
        self.lambda_kd = lambda_kd

    def reconstruction_loss(self, W_gen: torch.Tensor, W_target: torch.Tensor) -> torch.Tensor:
        return F.mse_loss(W_gen, W_target) * self.lambda_recon

    def distill_loss(self, logits_student: torch.Tensor, logits_teacher: torch.Tensor, T: float = 2.0) -> torch.Tensor:
        p_student = F.log_softmax(logits_student / T, dim=-1)
        p_teacher = F.softmax(logits_teacher / T, dim=-1)
        return F.kl_div(p_student, p_teacher, reduction='batchmean') * (T * T) * self.lambda_kd


# ------------------------ Quantization helpers -------------------------- #

def quantize_int8(tensor: torch.Tensor) -> Tuple[torch.Tensor, float]:
    # simple symmetric quantization
    maxv = tensor.abs().max()
    if maxv == 0:
        return tensor.to(torch.int8), 1.0
    scale = (127.0 / maxv).item()
    q = torch.clamp((tensor * scale).round(), -127, 127).to(torch.int8)
    return q, scale


def dequantize_int8(q_tensor: torch.Tensor, scale: float) -> torch.Tensor:
    return q_tensor.to(torch.float32) / scale


# ----------------------- AdaptiveHyperNet Core ------------------------- #

class AdaptiveHyperNet(nn.Module):
    def __init__(self,
                 seed_dim: int = 64,
                 latent_dim: int = 256,
                 max_rank: int = 64,
                 memory_size: int = 128,
                 context_dim: int = 128):
        super().__init__()
        self.seed_gen = SeedGenerator(seed_dim=seed_dim, latent_dim=latent_dim)
        self.expander = HyperExpander(latent_dim=latent_dim, max_rank=max_rank)
        self.memory = MemoryBank(max_items=memory_size)
        self.context_dim = context_dim
        self.reg = RegularizationEngine(lambda_recon=0.5, lambda_kd=0.5)

    def generate_lazy(self, seed: torch.Tensor, context: Optional[torch.Tensor], out_dim: int, in_dim: int, data_complexity: float) -> LazyWeight:
        # seed: [seed_dim] or [B, seed_dim] - for cache keep single example seeds
        rank = adaptive_rank(data_complexity, max_rank=self.expander.max_rank)

        def maker():
            latent = self.seed_gen(seed)
            U, V = self.expander(latent, out_dim, in_dim, rank=rank)
            # U: [out_dim, rank], V: [in_dim, rank]
            return LazyWeight(U, V)

        return self.memory.get_or_put(seed, context, maker)


# ---------------------- Example Adaptive Layer -------------------------- #

class AdaptiveLinear(nn.Module):
    """Linear layer that obtains its weights from AdaptiveHyperNet lazily.
    Supports per-batch context gating.
    """

    def __init__(self, in_dim: int, out_dim: int, hypernet: AdaptiveHyperNet, seed_dim: int = 64):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.hyper = hypernet
        # small learned seed per layer optionally combined with external seed
        self.layer_seed = nn.Parameter(torch.randn(seed_dim) * 0.01)
        self.gating = AdaptiveGating(dim=out_dim, k_frac=0.6)

    def forward(self, x: torch.Tensor, context: Optional[torch.Tensor] = None, data_complexity: float = 10.0) -> torch.Tensor:
        # x: [B, in_dim]
        seed = self.layer_seed
        lw = self.hyper.generate_lazy(seed, context, self.out_dim, self.in_dim, data_complexity)
        # apply gating
        if context is None:
            ctx = torch.zeros(self.hyper.context_dim, device=x.device)
        else:
            ctx = context
        mask = self.gating(ctx, hard=True)  # [out_dim]
        out = lw.matmul(x)
        out = out * mask
        return out


# ---------------------------- Benchmark --------------------------------- #

def tiny_benchmark():
    device = torch.device('cpu')
    seed_dim = 64
    hyper = AdaptiveHyperNet(seed_dim=seed_dim, latent_dim=128, max_rank=16, memory_size=64)
    layer = AdaptiveLinear(in_dim=128, out_dim=256, hypernet=hyper, seed_dim=seed_dim)
    layer.to(device)

    # toy workload
    B = 32
    x = torch.randn(B, 128, device=device)
    context = torch.randn(128, device=device)

    # warmup
    for _ in range(5):
        y = layer(x, context=context, data_complexity=100.0)

    # timed runs
    t0 = time.time()
    for _ in range(20):
        y = layer(x, context=context, data_complexity=100.0)
    t1 = time.time()
    print(f"Average forward time: {(t1 - t0)/20:.6f}s")
    print("Output shape:", y.shape)


# ------------------------------ Usage ----------------------------------- #

if __name__ == '__main__':
    print("Running tiny benchmark for AdaptiveHyperNet prototype")
    tiny_benchmark()

    # Example notes for extension:
    # - Replace _parametric_projection with learned small matrices for higher quality.
    # - Add proper per-batch generation and batching for multiple seeds.
    # - Replace the deterministic projection with a hash-table of small basis matrices.
    # - Implement quantized matmuls for cached quantized expansions.
    # - Add training script where G_theta learns to reconstruct teacher W_target or to minimize task loss directly.
