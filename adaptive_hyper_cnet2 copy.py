import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass
import logging

# =============================================================================
# CONFIGURATION & UTILITIES
# =============================================================================

@dataclass
class HyperNetConfig:
    """Konfiguracja modułu AdaptiveHyperNet - Enhanced z nowymi funkcjami"""
    seed_dim: int = 64
    max_rank: int = 128
    compression_ratio: float = 0.1
    rgb_channels: int = 3
    memory_bank_size: int = 1000
    adaptation_speed: float = 1.5
    regularization_strength: float = 0.001
    quantization_bits: int = 8
    sparsity_threshold: float = 0.1
    enable_caching: bool = True
    progressive_growth: bool = True

    # New enhanced features
    use_attention_mixing: bool = True      # Enable attention-based channel mixing
    use_learnable_semantics: bool = True   # Enable learnable RGB semantics
    attention_heads: int = 1               # Number of attention heads for channel mixing
    semantic_dim: int = 64                 # Dimension for semantic embeddings

def estimate_data_complexity(data_size: int, feature_dim: int, num_classes: int = 10) -> float:
    """Oszacowanie złożoności danych dla adaptacyjnego skalowania"""
    complexity = math.log(data_size + 1) * math.sqrt(feature_dim) * math.log(num_classes + 1)
    return min(complexity, 1000.0)

def adaptive_capacity_formula(data_complexity: float, base_capacity: int = 256) -> int:
    """Formuła na adaptacyjną pojemność modelu"""
    return int(base_capacity * (1 + data_complexity / 100))

# =============================================================================
# LEARNABLE RGB SEMANTICS - INNOWACJA #1A
# =============================================================================

class LearnableRGBSemantics(nn.Module):
    """
    Learnable semantics dla kanałów RGB - model sam uczy się znaczenia kanałów
    """
    def __init__(self, channels: int = 3, semantic_dim: int = 64):
        super().__init__()
        self.channels = channels
        self.semantic_dim = semantic_dim

        # Learnable embeddings dla każdego kanału
        self.channel_embeddings = nn.Parameter(torch.randn(channels, semantic_dim) * 0.02)

        # Semantic projector - mapuje kontekst zadania na wagi kanałów
        self.semantic_projector = nn.Sequential(
            nn.Linear(semantic_dim, semantic_dim * 2),
            nn.ReLU(),
            nn.Linear(semantic_dim * 2, channels),
            nn.Softmax(dim=-1)
        )

        # Task context encoder
        self.task_encoder = nn.Sequential(
            nn.Linear(semantic_dim, semantic_dim),
            nn.LayerNorm(semantic_dim),
            nn.ReLU(),
            nn.Linear(semantic_dim, semantic_dim)
        )

    def get_channel_weights(self, task_context: torch.Tensor) -> torch.Tensor:
        """
        Dynamicznie określa, który kanał jest ważniejszy dla danego zadania
        """
        # Encode task context
        encoded_context = self.task_encoder(task_context)  # [batch, semantic_dim]

        # Project encoded context to channel weights
        channel_weights = self.semantic_projector(encoded_context)  # [batch, channels]

        return channel_weights

    def get_semantic_interpretation(self) -> Dict[str, torch.Tensor]:
        """Zwraca interpretację semantyczną kanałów"""
        return {
            f'channel_{i}_embedding': self.channel_embeddings[i]
            for i in range(self.channels)
        }

# =============================================================================
# ATTENTION-BASED CHANNEL MIXING - INNOWACJA #1B
# =============================================================================

class AttentionChannelMixer(nn.Module):
    """
    Attention-based mixing kanałów RGB zamiast prostego MLP
    """
    def __init__(self, in_dim: int, channels: int = 3, num_heads: int = 1):
        super().__init__()
        self.channels = channels
        self.num_heads = num_heads

        # Multi-head attention dla kanałów
        self.channel_attention = nn.MultiheadAttention(
            embed_dim=channels,
            num_heads=num_heads,
            batch_first=True,
            dropout=0.1
        )

        # Context encoder - mapuje input na query/key/value dla attention
        self.context_encoder = nn.Sequential(
            nn.Linear(in_dim, channels * 3),  # Q, K, V
            nn.LayerNorm(channels * 3),
            nn.ReLU()
        )

        # Output projection
        self.output_proj = nn.Sequential(
            nn.Linear(channels, channels),
            nn.Softmax(dim=-1)
        )

    def forward(self, context_signal: torch.Tensor) -> torch.Tensor:
        """
        Args:
            context_signal: [batch, in_dim]
        Returns:
            channel_weights: [batch, channels]
        """
        batch_size = context_signal.size(0)

        # Encode context to Q, K, V
        qkv = self.context_encoder(context_signal)  # [batch, channels * 3]
        qkv = qkv.view(batch_size, 3, self.channels)  # [batch, 3, channels]

        query = qkv[:, 0, :].unsqueeze(1)  # [batch, 1, channels]
        key = qkv[:, 1, :].unsqueeze(1)    # [batch, 1, channels]
        value = qkv[:, 2, :].unsqueeze(1)  # [batch, 1, channels]

        # Self-attention over channels
        attended_channels, attention_weights = self.channel_attention(
            query, key, value
        )  # [batch, 1, channels]

        # Project to final channel weights
        channel_weights = self.output_proj(attended_channels.squeeze(1))  # [batch, channels]

        return channel_weights

# =============================================================================
# RGB WEIGHT CHANNELS - INNOWACJA #1 (ENHANCED)
# =============================================================================

class RGBWeightChannels(nn.Module):
    """
    Enhanced wielokanałowe wagi inspirowane RGB z attention i learnable semantics:
    - Channel 0 (R): Semantic features (learnable)
    - Channel 1 (G): Spatial patterns (learnable)
    - Channel 2 (B): Temporal dynamics (learnable)
    """
    def __init__(self, in_dim: int, out_dim: int, channels: int = 3, use_attention: bool = True, use_learnable_semantics: bool = True):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.channels = channels
        self.use_attention = use_attention
        self.use_learnable_semantics = use_learnable_semantics

        # Wielokanałowe wektory wag
        self.weight_vectors = nn.Parameter(torch.randn(out_dim, in_dim, channels) * 0.02)

        # Enhanced channel mixing components
        if use_attention:
            self.context_mixer = AttentionChannelMixer(in_dim, channels, num_heads=1)
        else:
            # Fallback to original MLP mixer
            self.context_mixer = nn.Sequential(
                nn.Linear(in_dim, channels * 4),
                nn.ReLU(),
                nn.Linear(channels * 4, channels),
                nn.Softmax(dim=-1)
            )

        # Learnable RGB semantics
        if use_learnable_semantics:
            semantic_dim = min(64, in_dim // 2)  # Adaptive semantic dimension
            self.rgb_semantics = LearnableRGBSemantics(channels, semantic_dim)
            self.semantic_adapter = nn.Linear(in_dim, semantic_dim)
        else:
            self.rgb_semantics = None
            self.semantic_adapter = None

        # Bias dla każdego kanału
        self.channel_bias = nn.Parameter(torch.zeros(out_dim, channels))
        
    def forward(self, x: torch.Tensor, context_signal: Optional[torch.Tensor] = None) -> torch.Tensor:
        batch_size = x.size(0)

        # Jeśli brak sygnału kontekstowego, użyj średniej z inputu
        if context_signal is None:
            context_signal = x.mean(dim=0, keepdim=True)
        else:
            # Jeśli context_signal ma niewłaściwe wymiary, dostosuj go
            if context_signal.dim() == 1:
                # Rozszerz context_signal do wymiarów [1, in_dim] poprzez padding/truncation
                if context_signal.size(0) < self.in_dim:
                    # Pad with zeros if too small
                    padding = torch.zeros(self.in_dim - context_signal.size(0), device=context_signal.device)
                    context_signal = torch.cat([context_signal, padding], dim=0)
                elif context_signal.size(0) > self.in_dim:
                    # Truncate if too large
                    context_signal = context_signal[:self.in_dim]
                context_signal = context_signal.unsqueeze(0)  # Add batch dimension

        # Enhanced channel mixing with attention and learnable semantics
        if self.use_attention:
            # Use attention-based channel mixing
            channel_mix = self.context_mixer(context_signal)  # [batch, channels]
        else:
            # Use original MLP-based mixing
            channel_mix = self.context_mixer(context_signal)  # [batch, channels]

        # Apply learnable RGB semantics if enabled
        if self.use_learnable_semantics and self.rgb_semantics is not None:
            # Adapt context to semantic space
            semantic_context = self.semantic_adapter(context_signal)  # [batch, semantic_dim]

            # Get semantic channel weights
            semantic_weights = self.rgb_semantics.get_channel_weights(semantic_context)  # [batch, channels]

            # Combine attention weights with semantic weights
            channel_mix = channel_mix * semantic_weights

            # Renormalize
            channel_mix = F.softmax(channel_mix, dim=-1)
        
        # Combine channels dynamically
        mixed_weights = torch.einsum('oic,bc->oib', self.weight_vectors, channel_mix)
        final_weights = mixed_weights.mean(dim=-1)  # [out_dim, in_dim]
        
        # Apply mixed bias
        mixed_bias = torch.einsum('oc,bc->ob', self.channel_bias, channel_mix).mean(dim=-1)
        
        return F.linear(x, final_weights, mixed_bias)

# =============================================================================
# LAZY WEIGHT GENERATOR - INNOWACJA #2
# =============================================================================

class LazyWeightTensor:
    """Lazy evaluation dla wag - materialization on demand"""
    def __init__(self, U: torch.Tensor, V: torch.Tensor, use_cache: bool = True):
        self.U = U  # [out_dim, rank]
        self.V = V  # [in_dim, rank]  
        self.use_cache = use_cache
        self._cached_weight = None
        self._cache_valid = False
        
    def materialize(self) -> torch.Tensor:
        """Zmaterializuj pełną macierz wag tylko gdy potrzeba"""
        if self.use_cache and self._cache_valid and self._cached_weight is not None:
            return self._cached_weight
            
        weight = torch.mm(self.U, self.V.t())  # [out_dim, in_dim]
        
        if self.use_cache:
            self._cached_weight = weight.detach().clone()
            self._cache_valid = True
            
        return weight
        
    def __call__(self, x: torch.Tensor) -> torch.Tensor:
        # Efficient matrix multiplication: (UV^T)x = U(V^T x)
        return torch.mm(self.U, torch.mm(self.V.t(), x.t())).t()

# =============================================================================
# SEED GENERATOR - CORE COMPONENT
# =============================================================================

class SeedGenerator(nn.Module):
    """Kompaktowy generator ziaren dla wag"""
    def __init__(self, config: HyperNetConfig):
        super().__init__()
        self.config = config
        
        # Core seed embedding
        self.seed_embedding = nn.Parameter(torch.randn(config.seed_dim) * 0.01)
        
        # Seed evolution network
        self.seed_evolution = nn.Sequential(
            nn.Linear(config.seed_dim, config.seed_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(config.seed_dim * 2, config.seed_dim),
            nn.LayerNorm(config.seed_dim)
        )
        
        # Context adaptation
        self.context_adapter = nn.Linear(config.seed_dim, config.seed_dim)
        
    def forward(self, layer_context: Optional[torch.Tensor] = None) -> torch.Tensor:
        evolved_seed = self.seed_evolution(self.seed_embedding)
        
        if layer_context is not None:
            # Adapt seed to layer context
            adapted_seed = self.context_adapter(evolved_seed) + layer_context
            return F.normalize(adapted_seed, dim=-1)
        
        return F.normalize(evolved_seed, dim=-1)

# =============================================================================
# HYPEREXPANDER - GŁÓWNY GENERATOR WAG
# =============================================================================

class HyperExpander(nn.Module):
    """Generator wag z adaptacyjną faktoryzacją low-rank"""
    def __init__(self, config: HyperNetConfig):
        super().__init__()
        self.config = config
        
        # U generator (output projector)
        self.u_generator = nn.Sequential(
            nn.Linear(config.seed_dim, config.seed_dim * 2),
            nn.ReLU(),
            nn.Linear(config.seed_dim * 2, config.max_rank * config.seed_dim),
            nn.Tanh()
        )
        
        # V generator (input projector)  
        self.v_generator = nn.Sequential(
            nn.Linear(config.seed_dim, config.seed_dim * 2),
            nn.ReLU(), 
            nn.Linear(config.seed_dim * 2, config.max_rank * config.seed_dim),
            nn.Tanh()
        )
        
        # Rank controller - adaptacyjne dostosowanie rangi
        self.rank_controller = nn.Sequential(
            nn.Linear(config.seed_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def adaptive_rank(self, seed: torch.Tensor, data_complexity: float) -> int:
        """Dynamiczne dostosowanie rangi na podstawie złożoności"""
        base_rank = int(self.rank_controller(seed).item() * self.config.max_rank)
        complexity_factor = min(data_complexity / 100.0, 2.0)
        return max(4, min(self.config.max_rank, int(base_rank * complexity_factor)))
    
    def generate_factors(self, seed: torch.Tensor, out_dim: int, in_dim: int, 
                        data_complexity: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generuj macierze U i V dla faktoryzacji low-rank"""
        rank = self.adaptive_rank(seed, data_complexity)
        
        # Generate U matrix [out_dim, rank]
        u_flat = self.u_generator(seed)
        u_matrix = u_flat.view(-1, rank)[:out_dim, :]
        u_matrix = F.normalize(u_matrix, dim=-1) * math.sqrt(rank)
        
        # Generate V matrix [in_dim, rank]
        v_flat = self.v_generator(seed)
        v_matrix = v_flat.view(-1, rank)[:in_dim, :]
        v_matrix = F.normalize(v_matrix, dim=-1) * math.sqrt(rank)
        
        return u_matrix, v_matrix

# =============================================================================
# ADAPTIVE GATING - INNOWACJA #3
# =============================================================================

class AdaptiveGating(nn.Module):
    """Dynamiczne włączanie/wyłączanie neuronów z Top-K sparsity"""
    def __init__(self, dim: int, sparsity_ratio: float = 0.1):
        super().__init__()
        self.dim = dim
        self.sparsity_ratio = sparsity_ratio
        
        # Gate controller
        self.gate_network = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.ReLU(),
            nn.Linear(dim // 4, dim),
            nn.Sigmoid()
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.size(0)
        
        # Compute gates
        gates = self.gate_network(x.mean(dim=0, keepdim=True))
        
        # Top-K sparsity
        k = max(1, int(self.dim * (1 - self.sparsity_ratio)))
        topk_values, topk_indices = torch.topk(gates, k, dim=-1)
        
        # Create sparse mask
        sparse_mask = torch.zeros_like(gates)
        sparse_mask.scatter_(-1, topk_indices, 1.0)
        
        return x * sparse_mask

# =============================================================================
# MEMORY BANK - CACHE SYSTEM
# =============================================================================

class MemoryBank(nn.Module):
    """Cache dla często używanych wzorców wag"""
    def __init__(self, config: HyperNetConfig):
        super().__init__()
        self.config = config
        self.max_size = config.memory_bank_size
        
        # Memory storage
        self.register_buffer('memory_keys', torch.zeros(self.max_size, config.seed_dim))
        self.register_buffer('memory_values', torch.zeros(self.max_size, config.seed_dim * 2))
        self.register_buffer('usage_count', torch.zeros(self.max_size))
        self.register_buffer('current_size', torch.tensor(0))
        
    def query(self, seed: torch.Tensor, threshold: float = 0.95) -> Optional[torch.Tensor]:
        """Zapytaj cache o podobne ziarna"""
        if self.current_size == 0:
            return None
            
        # Cosine similarity
        similarities = F.cosine_similarity(
            seed.unsqueeze(0), 
            self.memory_keys[:self.current_size], 
            dim=1
        )
        
        max_sim, max_idx = torch.max(similarities, dim=0)
        
        if max_sim > threshold:
            self.usage_count[max_idx] += 1
            return self.memory_values[max_idx]
            
        return None
    
    def store(self, seed: torch.Tensor, value: torch.Tensor):
        """Zapisz do cache"""
        if self.current_size < self.max_size:
            idx = self.current_size
            self.current_size += 1
        else:
            # Replace least used
            idx = torch.argmin(self.usage_count)
            
        self.memory_keys[idx] = seed.detach()
        self.memory_values[idx] = value.detach()
        self.usage_count[idx] = 1

# =============================================================================
# GŁÓWNY MODUŁ ADAPTIVEHYPERNET
# =============================================================================

class AdaptiveHyperNet(nn.Module):
    """
    Główny moduł AdaptiveHyperNet - uniwersalny generator sieci neuronowych
    """
    def __init__(self, config: HyperNetConfig, task_type: str = "classification"):
        super().__init__()
        self.config = config
        self.task_type = task_type
        
        # Core components
        self.seed_generator = SeedGenerator(config)
        self.hyper_expander = HyperExpander(config)
        self.memory_bank = MemoryBank(config) if config.enable_caching else None
        
        # Layer registry
        self.layers = nn.ModuleList()
        self.layer_configs = []
        
        # Statistics
        self.register_buffer('data_complexity', torch.tensor(1.0))
        self.register_buffer('current_capacity', torch.tensor(config.seed_dim))
        
        # Regularization
        self.dropout = nn.Dropout(0.1)
        
    def add_layer(self, in_dim: int, out_dim: int, layer_type: str = "linear") -> int:
        """Dodaj warstwę do sieci"""
        layer_id = len(self.layers)
        
        if layer_type == "rgb":
            layer = RGBWeightChannels(in_dim, out_dim, self.config.rgb_channels)
        elif layer_type == "adaptive":
            layer = AdaptiveHyperNetLayer(in_dim, out_dim, self.config)
        else:  # linear
            layer = HyperLinearLayer(in_dim, out_dim, self.config)
            
        self.layers.append(layer)
        self.layer_configs.append({
            'in_dim': in_dim,
            'out_dim': out_dim, 
            'type': layer_type,
            'layer_id': layer_id
        })
        
        return layer_id
        
    def update_complexity(self, data_size: int, feature_dim: int, num_classes: int = 10):
        """Aktualizuj złożoność danych i dostosuj pojemność"""
        new_complexity = estimate_data_complexity(data_size, feature_dim, num_classes)
        self.data_complexity = torch.tensor(new_complexity)
        
        if self.config.progressive_growth:
            new_capacity = adaptive_capacity_formula(new_complexity, self.config.seed_dim)
            self.current_capacity = torch.tensor(new_capacity)
            
    def forward(self, x: torch.Tensor, layer_id: int = 0) -> torch.Tensor:
        """Forward pass przez wybraną warstwę"""
        if layer_id >= len(self.layers):
            raise IndexError(f"Layer {layer_id} not found")
            
        layer = self.layers[layer_id]
        
        # Generate context for this layer
        layer_context = self._generate_layer_context(layer_id)
        
        if isinstance(layer, (RGBWeightChannels, AdaptiveHyperNetLayer)):
            return layer(x, layer_context)
        else:
            return layer(x)
    
    def _generate_layer_context(self, layer_id: int) -> torch.Tensor:
        """Generuj kontekst dla konkretnej warstwy"""
        base_context = torch.randn(self.config.seed_dim) * 0.01
        layer_embedding = torch.tensor(layer_id, dtype=torch.float32) * 0.1
        
        return base_context + layer_embedding
        
    def get_model_stats(self) -> Dict[str, Any]:
        """Zwróć statystyki modelu"""
        total_params = sum(p.numel() for p in self.parameters())
        active_layers = len(self.layers)
        
        return {
            'total_parameters': total_params,
            'active_layers': active_layers,
            'data_complexity': self.data_complexity.item(),
            'current_capacity': self.current_capacity.item(),
            'compression_ratio': self.config.compression_ratio,
            'memory_usage_mb': total_params * 4 / 1024 / 1024
        }

# =============================================================================
# SPECIALIZED LAYERS
# =============================================================================

class HyperLinearLayer(nn.Module):
    """Liniowa warstwa z generowanymi wagami"""
    def __init__(self, in_dim: int, out_dim: int, config: HyperNetConfig):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.config = config
        
        # Bias jako normalny parametr
        self.bias = nn.Parameter(torch.zeros(out_dim))
        
        # Gating dla sparsity
        self.gating = AdaptiveGating(out_dim, config.sparsity_threshold)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Tu należałoby wygenerować wagi z seed_generator i hyper_expander
        # Dla uproszczenia użyjmy stałą macierz
        weight = torch.randn(self.out_dim, self.in_dim) * 0.02
        output = F.linear(x, weight, self.bias)
        return self.gating(output)

class AdaptiveHyperNetLayer(nn.Module):
    """Pełna adaptacyjna warstwa z wszystkimi innowacjami - Enhanced z attention i learnable semantics"""
    def __init__(self, in_dim: int, out_dim: int, config: HyperNetConfig, use_attention: bool = True, use_learnable_semantics: bool = True):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.config = config

        # Enhanced RGB weights with attention and learnable semantics
        self.rgb_weights = RGBWeightChannels(
            in_dim, out_dim, config.rgb_channels,
            use_attention=use_attention,
            use_learnable_semantics=use_learnable_semantics
        )
        self.gating = AdaptiveGating(out_dim, config.sparsity_threshold)
        self.layer_norm = nn.LayerNorm(out_dim)
        
    def forward(self, x: torch.Tensor, context_signal: Optional[torch.Tensor] = None) -> torch.Tensor:
        # Enhanced forward pass with context support
        output = self.rgb_weights(x, context_signal)
        output = self.gating(output)
        return self.layer_norm(output)

    def get_semantic_interpretation(self) -> Optional[Dict[str, torch.Tensor]]:
        """Zwraca interpretację semantyczną kanałów RGB jeśli dostępna"""
        if hasattr(self.rgb_weights, 'rgb_semantics') and self.rgb_weights.rgb_semantics is not None:
            return self.rgb_weights.rgb_semantics.get_semantic_interpretation()
        return None

# =============================================================================
# TRAINING UTILITIES
# =============================================================================

class AdaptiveTrainer:
    """Trainer z automatyczną regularyzacją i anti-overfitting"""
    def __init__(self, model: AdaptiveHyperNet, config: HyperNetConfig):
        self.model = model
        self.config = config
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def compute_adaptive_loss(self, pred: torch.Tensor, target: torch.Tensor, 
                             epoch: int) -> torch.Tensor:
        """Adaptacyjna funkcja straty z regularyzacją"""
        # Base task loss
        base_loss = F.cross_entropy(pred, target)
        
        # Complexity penalty
        complexity_penalty = self.config.regularization_strength * self.model.data_complexity
        
        # Progressive regularization
        prog_reg = (1 - math.exp(-epoch / 100)) * 0.01
        
        return base_loss + complexity_penalty + prog_reg

# =============================================================================
# EXAMPLE USAGE & DEMO
# =============================================================================

def create_demo_model():
    """Stwórz przykładowy model do demonstracji z enhanced features"""
    config = HyperNetConfig(
        seed_dim=128,
        max_rank=64,
        compression_ratio=0.1,
        rgb_channels=3,
        progressive_growth=True,
        # Enhanced features
        use_attention_mixing=True,
        use_learnable_semantics=True,
        attention_heads=1,
        semantic_dim=64
    )
    
    # Create model
    model = AdaptiveHyperNet(config, task_type="classification")
    
    # Add layers
    model.add_layer(784, 256, "rgb")      # Input layer z RGB weights
    model.add_layer(256, 128, "adaptive") # Hidden layer z gating
    model.add_layer(128, 10, "linear")    # Output layer
    
    # Update complexity based on MNIST-like data
    model.update_complexity(data_size=60000, feature_dim=784, num_classes=10)
    
    return model

def demonstrate_enhanced_features(model: AdaptiveHyperNet):
    """Demonstracja nowych funkcji: attention i learnable semantics"""
    print("\n🔬 Demonstracja Enhanced Features:")
    print("="*50)

    # Test attention-based channel mixing
    print("🎯 Attention-Based Channel Mixing:")

    # Get first adaptive layer and determine correct input size
    adaptive_layers = [layer for layer in model.layers if isinstance(layer, AdaptiveHyperNetLayer)]
    if adaptive_layers:
        adaptive_layer = adaptive_layers[0]
        # Use the correct input dimension for this layer
        test_input = torch.randn(1, adaptive_layer.in_dim)

        # Test with different contexts (use seed_dim from config)
        seed_dim = 128  # From config
        contexts = [
            torch.randn(seed_dim) * 0.1,  # Semantic context
            torch.randn(seed_dim) * 0.5,  # Spatial context
            torch.randn(seed_dim) * 1.0,  # Temporal context
        ]

        context_names = ["Semantic", "Spatial", "Temporal"]

        for context, name in zip(contexts, context_names):
            output = adaptive_layer(test_input, context)
            print(f"   {name} context output shape: {output.shape}")
            print(f"   {name} context mean activation: {output.mean().item():.4f}")

        # Get semantic interpretation if available
        semantic_info = adaptive_layer.get_semantic_interpretation()
        if semantic_info:
            print("\n🧠 Learnable RGB Semantics:")
            for channel_name, embedding in semantic_info.items():
                norm = torch.norm(embedding).item()
                print(f"   {channel_name} embedding norm: {norm:.4f}")

                # Show top 3 dimensions with highest values
                top_dims = torch.topk(torch.abs(embedding), 3)
                print(f"   {channel_name} top dimensions: {top_dims.indices.tolist()}")
        else:
            print("   Learnable semantics not available")
    else:
        print("   No adaptive layers found")

    print("\n✨ Enhanced features demonstration complete!")

def benchmark_model(model: AdaptiveHyperNet, input_size: Tuple[int, ...]):
    """Benchmark wydajności modelu"""
    model.eval()
    
    # Generate test input
    test_input = torch.randn(32, *input_size)
    
    import time
    
    # Warmup
    for _ in range(10):
        _ = model(test_input, layer_id=0)
    
    # Benchmark
    start_time = time.time()
    for _ in range(100):
        _ = model(test_input, layer_id=0)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 100
    stats = model.get_model_stats()
    
    print("🚀 BENCHMARK RESULTS:")
    print(f"   Average inference time: {avg_time*1000:.2f}ms")
    print(f"   Total parameters: {stats['total_parameters']:,}")
    print(f"   Memory usage: {stats['memory_usage_mb']:.2f}MB")
    print(f"   Compression ratio: {stats['compression_ratio']}")
    print(f"   Data complexity: {stats['data_complexity']:.2f}")

if __name__ == "__main__":
    print("🧠 AdaptiveHyperNet - Innowacyjny Moduł Sieci Neuronowych")
    print("="*60)
    
    # Create demo model
    model = create_demo_model()
    print("✅ Model stworzony pomyślnie!")
    
    # Print stats
    stats = model.get_model_stats()
    print(f"\n📊 Statystyki modelu:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Benchmark
    print(f"\n⚡ Benchmark wydajności:")
    benchmark_model(model, (784,))

    # Demonstrate enhanced features
    demonstrate_enhanced_features(model)

    print("\n🎯 Enhanced AdaptiveHyperNet gotowy do treningu!")
    print("💡 Nowe funkcje: Attention-based channel mixing + Learnable RGB semantics!")
    print("� Wykorzystuje wagi RGB, adaptacyjne skalowanie, lazy evaluation i memory bank!")